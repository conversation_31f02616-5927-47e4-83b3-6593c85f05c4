@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations and styles for EduPro landing page */

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Custom fade-in animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Modern Auth Modal Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes modalBackdrop {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(12px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes pulse-border {
  0%, 100% {
    border-color: rgb(99 102 241 / 0.3);
    box-shadow: 0 0 0 0 rgb(99 102 241 / 0.7);
  }
  70% {
    border-color: rgb(99 102 241 / 0.6);
    box-shadow: 0 0 0 3px rgb(99 102 241 / 0);
  }
}

/* Animation classes */
.animate-fadeInUp {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out forwards;
}

.animate-slideInLeft {
  animation: slideInFromLeft 0.8s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInFromRight 0.8s ease-out forwards;
}

.animate-float {
  animation: floatAnimation 3s ease-in-out infinite;
}

.animate-pulseGlow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.animate-modalBackdrop {
  animation: modalBackdrop 0.3s ease-out;
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  background-size: 200px 100%;
  animation: shimmer 2s infinite;
}

.animate-pulse-border {
  animation: pulse-border 2s infinite;
}

/* Hover effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Gradient text effect */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Custom button styles */
.btn-primary {
  @apply bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition-all duration-200 hover:from-indigo-700 hover:to-purple-700 hover:shadow-xl transform hover:-translate-y-1;
}

.btn-secondary {
  @apply border-2 border-indigo-600 text-indigo-600 font-semibold py-3 px-6 rounded-lg transition-all duration-200 hover:bg-indigo-600 hover:text-white transform hover:-translate-y-1;
}

/* Loading state for form submissions */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Enhanced focus states for accessibility */
.focus-visible:focus {
  outline: 2px solid #6366f1;
  outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Dark theme scrollbar */
.dark ::-webkit-scrollbar-track {
  background: #1e293b;
}

.dark ::-webkit-scrollbar-thumb {
  background: #475569;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Grid pattern for hero section */
.bg-grid-pattern {
  background-image: radial-gradient(circle, #e2e8f0 1px, transparent 1px);
  background-size: 20px 20px;
}

/* Enhanced form field styles */
.form-field-enhanced {
  @apply relative;
}

.form-field-enhanced input:focus + .field-icon {
  @apply text-indigo-500 scale-110;
}

.form-field-enhanced:hover .field-icon {
  @apply text-slate-500;
}

/* Loading spinner enhancement */
@keyframes spin-enhanced {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-spinner-enhanced {
  animation: spin-enhanced 1s linear infinite;
}
