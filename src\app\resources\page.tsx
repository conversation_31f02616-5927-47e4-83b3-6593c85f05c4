'use client';

import { useState } from 'react';
import AuthModal from '../../components/auth/auth-modal';
import LandingFooter from '../../components/landing/landing-footer';
import LandingHeader from '../../components/landing/landing-header';

export default function ResourcesPage() {
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState('all');

  const handleOpenAuthModal = () => {
    setIsAuthModalOpen(true);
  };

  const handleCloseAuthModal = () => {
    setIsAuthModalOpen(false);
  };

  const categories = [
    { id: 'all', name: 'All Resources', count: 24 },
    { id: 'guides', name: 'User Guides', count: 8 },
    { id: 'videos', name: 'Video Tutorials', count: 6 },
    { id: 'templates', name: 'Templates', count: 5 },
    { id: 'best-practices', name: 'Best Practices', count: 5 }
  ];

  const resources = [
    {
      category: 'guides',
      title: 'Getting Started with EduPro',
      description: 'Complete setup guide for new administrators',
      type: 'PDF Guide',
      duration: '15 min read',
      featured: true,
      image: '📚'
    },
    {
      category: 'videos',
      title: 'Student Enrollment Process',
      description: 'Step-by-step video walkthrough',
      type: 'Video Tutorial',
      duration: '8 min watch',
      featured: true,
      image: '🎬'
    },
    {
      category: 'templates',
      title: 'Report Card Templates',
      description: 'Customizable templates for different grade levels',
      type: 'Template Pack',
      duration: '5 templates',
      featured: false,
      image: '📊'
    },
    {
      category: 'best-practices',
      title: 'Data Security Guidelines',
      description: 'Best practices for protecting student information',
      type: 'Security Guide',
      duration: '12 min read',
      featured: true,
      image: '🔒'
    },
    {
      category: 'guides',
      title: 'Grade Book Management',
      description: 'Comprehensive guide to managing grades and assessments',
      type: 'User Guide',
      duration: '20 min read',
      featured: false,
      image: '📝'
    },
    {
      category: 'videos',
      title: 'Parent Portal Setup',
      description: 'How to configure and customize the parent portal',
      type: 'Video Tutorial',
      duration: '12 min watch',
      featured: false,
      image: '👨‍👩‍👧‍👦'
    }
  ];

  const filteredResources = selectedCategory === 'all' 
    ? resources 
    : resources.filter(resource => resource.category === selectedCategory);

  return (    <div className="min-h-screen flex flex-col bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 text-slate-900 font-sans">
      <LandingHeader onOpenAuthModal={handleOpenAuthModal} />
      <main className="flex-grow">        {/* Hero Section with Search */}
        <section className="bg-gradient-to-r from-pink-100 via-purple-100 to-indigo-100 text-slate-800 py-16 md:py-20" style={{background: 'linear-gradient(135deg, #fdf2f8 0%, #f3f0ff 50%, #eef2ff 100%)'}}>
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-2xl md:text-3xl font-bold mb-6" style={{color: '#8b5cf6'}}>
                Knowledge Center
              </h1>
              <p className="text-base mb-8 leading-relaxed" style={{color: '#6d28d9'}}>
                Find guides, tutorials, and resources to maximize your EduPro experience
              </p>

              {/* Search Bar */}
              <div className="max-w-2xl mx-auto mb-8">
                <div className="relative">
                  <input
                    type="text"
                    placeholder="Search documentation, guides, tutorials..."
                    className="w-full px-6 py-4 rounded-xl text-slate-900 bg-white shadow-lg focus:outline-none focus:ring-4 focus:ring-purple-200 pl-14 text-sm"
                  />
                  <svg className="absolute left-5 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-2xl mx-auto">
                <div className="bg-white/80 backdrop-blur rounded-lg p-4" style={{backgroundColor: 'rgba(255, 255, 255, 0.8)'}}>
                  <div className="text-lg font-bold" style={{color: '#f472b6'}}>50+</div>
                  <div className="text-sm" style={{color: '#ec4899'}}>Guides</div>
                </div>
                <div className="bg-white/80 backdrop-blur rounded-lg p-4" style={{backgroundColor: 'rgba(255, 255, 255, 0.8)'}}>
                  <div className="text-lg font-bold" style={{color: '#a78bfa'}}>25+</div>
                  <div className="text-sm" style={{color: '#8b5cf6'}}>Videos</div>
                </div>
                <div className="bg-white/80 backdrop-blur rounded-lg p-4" style={{backgroundColor: 'rgba(255, 255, 255, 0.8)'}}>
                  <div className="text-lg font-bold" style={{color: '#60a5fa'}}>15+</div>
                  <div className="text-sm" style={{color: '#3b82f6'}}>Templates</div>
                </div>
                <div className="bg-white/80 backdrop-blur rounded-lg p-4" style={{backgroundColor: 'rgba(255, 255, 255, 0.8)'}}>
                  <div className="text-lg font-bold" style={{color: '#34d399'}}>24/7</div>
                  <div className="text-sm" style={{color: '#10b981'}}>Support</div>
                </div>
              </div>
            </div>
          </div>
        </section>{/* Main Content Area */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 items-start">              {/* Sidebar with Categories */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl shadow-sm border border-slate-200 p-6 sticky top-24">
                  <h3 className="font-semibold text-base mb-4 text-slate-900">Categories</h3>
                  <div className="space-y-2">
                    {categories.map((category) => (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`w-full flex justify-between items-center px-4 py-3 rounded-lg text-left transition-colors text-sm ${
                          selectedCategory === category.id
                            ? 'bg-pink-100 text-pink-900 border border-pink-200'
                            : 'hover:bg-slate-50 text-slate-700'
                        }`}
                      >
                        <span className="font-medium">{category.name}</span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          selectedCategory === category.id
                            ? 'bg-pink-200 text-pink-800'
                            : 'bg-slate-200 text-slate-600'
                        }`}>
                          {category.count}
                        </span>
                      </button>
                    ))}
                  </div>

                  {/* Quick Actions */}
                  <div className="mt-8 pt-6 border-t border-slate-200">
                    <h4 className="font-semibold mb-4 text-slate-900 text-sm">Quick Actions</h4>
                    <div className="space-y-3">
                      <button 
                        onClick={handleOpenAuthModal}
                        className="w-full bg-gradient-to-r from-pink-400 to-purple-500 text-white px-4 py-2 rounded-lg hover:from-pink-500 hover:to-purple-600 transition-all duration-200 text-sm"
                      >
                        Start Free Trial
                      </button>
                      <button className="w-full border border-purple-300 text-purple-700 px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors text-sm">
                        Contact Support
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Main Content */}
              <div className="lg:col-span-3">                {/* Featured Resources Banner */}
                <div className="bg-gradient-to-r from-pink-400 via-purple-500 to-indigo-500 rounded-xl p-8 mb-8 text-white">
                  <div className="flex flex-col lg:flex-row items-center justify-between">
                    <div className="lg:w-2/3">
                      <h2 className="text-lg font-bold mb-2">🚀 New Administrator Training Series</h2>
                      <p className="text-pink-100 mb-4 text-sm">
                        Complete 5-part video series covering everything new admins need to know
                      </p>
                      <button className="bg-white text-pink-600 px-6 py-2 rounded-lg font-semibold hover:bg-pink-50 transition-colors text-sm">
                        Watch Now
                      </button>
                    </div>
                    <div className="lg:w-1/3 mt-4 lg:mt-0 text-center">
                      <div className="text-3xl mb-2">🎓</div>
                      <div className="text-sm text-pink-200">5 Videos • 45 min total</div>
                    </div>
                  </div>
                </div>

                {/* Resource Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {filteredResources.map((resource, index) => (
                    <div
                      key={index}                      className={`bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-all duration-300 hover:-translate-y-1 ${
                        resource.featured ? 'ring-2 ring-pink-200' : ''
                      }`}
                    >                      {resource.featured && (
                        <div className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-4 py-1 text-xs font-semibold">
                          FEATURED
                        </div>
                      )}
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="text-2xl">{resource.image}</div>
                          <span className="bg-slate-100 text-slate-600 px-3 py-1 rounded-full text-xs font-medium">
                            {resource.type}
                          </span>
                        </div>                        <h3 className="font-semibold text-base mb-2 text-slate-900">
                          {resource.title}
                        </h3>
                        <p className="text-slate-600 text-sm mb-4 leading-relaxed">
                          {resource.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-slate-500 flex items-center">
                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            {resource.duration}
                          </span>
                          <button className="text-purple-500 hover:text-purple-700 font-medium text-sm flex items-center group">
                            Access
                            <svg className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                            </svg>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>                {/* Load More Button */}
                <div className="text-center mt-12">
                  <button className="bg-gradient-to-r from-purple-100 to-pink-100 text-slate-700 px-8 py-3 rounded-lg hover:from-purple-200 hover:to-pink-200 transition-all duration-200 font-medium text-sm">
                    Load More Resources
                  </button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Support CTA Section */}
        <section className="bg-white py-16 border-t border-slate-200">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">                <div>
                  <h2 className="text-2xl font-bold mb-4 text-slate-900">
                    Need Personal Assistance?
                  </h2>
                  <p className="text-slate-600 mb-6 leading-relaxed text-sm">
                    Our expert support team is available 24/7 to help you get the most out of EduPro. 
                    Whether you need technical assistance or implementation guidance, we're here for you.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-4">
                    <button className="bg-gradient-to-r from-pink-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:from-pink-600 hover:to-purple-700 transition-all duration-200 font-medium text-sm">
                      Contact Support
                    </button>
                    <button 
                      onClick={handleOpenAuthModal}
                      className="border border-purple-400 text-purple-600 px-6 py-3 rounded-lg hover:bg-purple-50 transition-colors font-medium text-sm"
                    >
                      Schedule Demo
                    </button>
                  </div>
                </div>                <div className="bg-gradient-to-br from-purple-50 to-pink-50 rounded-xl p-8">
                  <div className="grid grid-cols-2 gap-6">
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-500 mb-1">24/7</div>
                      <div className="text-sm text-slate-600">Support Available</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-pink-500 mb-1">&lt;2min</div>
                      <div className="text-sm text-slate-600">Average Response</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-indigo-500 mb-1">95%</div>
                      <div className="text-sm text-slate-600">Satisfaction Rate</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-500 mb-1">1000+</div>
                      <div className="text-sm text-slate-600">Schools Supported</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <LandingFooter />
      
      {/* Auth Modal */}
      <AuthModal 
        isOpen={isAuthModalOpen} 
        onClose={handleCloseAuthModal}
        defaultTab="signup"
      />
    </div>
  );
}
