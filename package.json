{"name": "edupro", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "dependencies": {"@supabase/supabase-js": "^2.49.8", "next": "^15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^3.4.1"}, "devDependencies": {"@types/node": "^20.11.20", "@types/react": "^18.2.58", "autoprefixer": "^10.4.17", "eslint": "^8.57.0", "eslint-config-next": "^15.3.3", "postcss": "^8.4.35", "prettier": "^3.2.5", "typescript": "^5.3.3"}}