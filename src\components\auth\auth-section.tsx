// src/components/auth/auth-section.tsx
'use client';

import { useEffect, useState } from 'react';
import AuthForm from './auth-form';

interface AuthSectionProps {
  activeTab: 'login' | 'signup';
  onToggleForm?: (tab: 'login' | 'signup') => void;
}

const testimonials = [
  {
    quote: "EduPro has transformed our school's administration. It's intuitive, powerful, and has saved us countless hours.",
    name: "Dr. <PERSON>",
    title: "Principal, Northwood High",
    avatarInitials: "JF",
  },
  {
    quote: "As a teacher, EduPro's gradebook and communication tools are indispensable. My students and their parents love it!",
    name: "<PERSON>",
    title: "Lead Teacher, Hill Valley School",
    avatarInitials: "M<PERSON>",
  },
  {
    quote: "The parent portal is fantastic! I can easily track my child's progress and communicate with teachers. Highly recommended!",
    name: "<PERSON>",
    title: "Parent, SkyNet Academy",
    avatarInitials: "SC",
  },
];

const AuthSection = ({ activeTab, onToggleForm }: AuthSectionProps) => {
  const [currentTestimonial, setCurrentTestimonial] = useState(testimonials[0]);
  const [testimonialIndex, setTestimonialIndex] = useState(0);

  useEffect(() => {
    const timer = setInterval(() => {
      setTestimonialIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 7000); // Change testimonial every 7 seconds
    return () => clearInterval(timer);
  }, []);

  useEffect(() => {
    setCurrentTestimonial(testimonials[testimonialIndex]);
  }, [testimonialIndex]);
  return (
    <section id="auth" className="py-12 md:py-20 bg-slate-50">
      <div className="container mx-auto max-w-6xl bg-white rounded-xl shadow-2xl overflow-hidden border border-slate-200 animate-fadeInUp">
        <div className="grid grid-cols-1 lg:grid-cols-2 min-h-[600px]">          {/* Left Side: Auth Form */}
          <div className="p-6 md:p-10 flex flex-col justify-center animate-slideInLeft">
            <div className="max-w-md mx-auto w-full">
              <h2 className="text-3xl font-bold text-slate-900 mb-3 text-center">
                {activeTab === 'login' ? 'Welcome Back to EduPro' : 'Join EduPro Today'}
              </h2>
              <p className="text-slate-600 mb-8 text-center text-sm">
                {activeTab === 'login'
                  ? 'Sign in to access your dashboard and manage your school effortlessly.'
                  : 'Create an account to unlock powerful school management features.'}
              </p>
              <AuthForm formType={activeTab} onToggleForm={onToggleForm} />
            </div>
          </div>          {/* Right Side: Testimonials & Product Bylines */}
          <div className="hidden lg:flex flex-col justify-center bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500 p-10 text-white relative overflow-hidden animate-slideInRight">
            {/* Abstract background pattern */}
            <div className="absolute inset-0 opacity-10">
                <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg"><defs><pattern id="smallGrid" width="20" height="20" patternUnits="userSpaceOnUse"><path d="M 20 0 L 0 0 0 20" fill="none" stroke="white" strokeWidth="0.5"/></pattern></defs><rect width="100%" height="100%" fill="url(#smallGrid)" /></svg>
            </div>
            
            <div className="relative z-10 transition-all duration-700 ease-in-out" key={testimonialIndex}>
              <h3 className="text-3xl font-bold mb-6 leading-tight">
                Empowering Educational Excellence, <br /> One Click at a Time.
              </h3>
              <p className="text-indigo-100 text-lg mb-10 leading-relaxed">
                EduPro is more than just software; it's a partner in creating a smarter, 
                more connected, and efficient learning environment for everyone.
              </p>
              
              <div className="bg-white/10 backdrop-blur-md p-6 rounded-lg shadow-lg border border-white/20">
                <blockquote className="text-xl italic mb-4">
                  <span className="absolute -left-3 -top-2 text-5xl text-white/50 opacity-50">“</span>
                  {currentTestimonial.quote}
                </blockquote>
                <div className="flex items-center mt-6">
                  <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center text-xl font-semibold mr-4">
                    {currentTestimonial.avatarInitials}
                  </div>
                  <div>
                    <p className="font-semibold text-md">{currentTestimonial.name}</p>
                    <p className="text-indigo-200 text-sm">{currentTestimonial.title}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AuthSection;
