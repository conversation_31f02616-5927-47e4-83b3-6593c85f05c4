// src/components/landing/landing-header.tsx
'use client';

import { useState } from 'react';

interface LandingHeaderProps {
  onOpenAuthModal: () => void;
}

const LandingHeader = ({ onOpenAuthModal }: LandingHeaderProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (    <header className="bg-slate-900 text-white p-4 shadow-md sticky top-0 z-50 font-sans">
      <div className="container mx-auto flex items-center">
        <div className="flex items-center space-x-2">
          {/* Abstract Logo - Graduation Cap Icon */}
          <svg
            width="36"
            height="36"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-indigo-400"
          >
            <path
              d="M22 9L12 14L2 9L12 4L22 9ZM12 15.5L6 12.5V16.5L12 19.5L18 16.5V12.5L12 15.5Z"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M20 11V15C20 15.093 19.991 15.185 19.974 15.274M12 19.5L4 15V11"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M4 9.5V15L12 19.5L20 15V9.5L12 14L4 9.5Z"
              fill="currentColor"
              fillOpacity="0.3"
            />
          </svg>
          <h1 className="text-xl font-bold">EduPro</h1>
        </div>        
        {/* Desktop Navigation - Moved closer to center */}
        <nav className="hidden md:flex space-x-6 items-center ml-auto mr-8">
          <a href="/product" className="hover:text-indigo-300 transition-colors text-base">Product</a>
          <a href="/resources" className="hover:text-indigo-300 transition-colors text-base">Resources</a>          <button
            onClick={onOpenAuthModal}
            className="ml-2 px-3 py-1 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg text-base font-medium transition-all"
          >
            Login
          </button>
        </nav>        {/* Mobile Menu Button */}
        <div className="md:hidden ml-auto">
          <button onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)} className="text-white focus:outline-none p-2">
            {isMobileMenuOpen ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path></svg>
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16m-7 6h7"></path></svg>
            )}
          </button>
        </div>
      </div>      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden mt-3 bg-slate-800 rounded-lg shadow-xl p-4 absolute top-full left-0 right-0 mx-2">
          <nav className="flex flex-col space-y-3">            <a href="/product" className="hover:text-indigo-300 transition-colors block py-2 text-base" onClick={() => setIsMobileMenuOpen(false)}>Product</a>
            <a href="/resources" className="hover:text-indigo-300 transition-colors block py-2 text-base" onClick={() => setIsMobileMenuOpen(false)}>Resources</a>
            <div className="flex flex-col space-y-2 pt-3 mt-2 border-t border-slate-700">              <button
                onClick={() => { onOpenAuthModal(); setIsMobileMenuOpen(false); }}
                className="w-full px-4 py-2.5 rounded-md text-base font-medium transition-all text-left bg-indigo-600 text-white hover:bg-indigo-700"
              >
                Login
              </button>
            </div>
          </nav>
        </div>
      )}
    </header>
  );
};

export default LandingHeader;
