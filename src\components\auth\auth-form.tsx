// src/components/auth/auth-form.tsx
'use clien          className="flex-1 flex items-center justify-center px-3 py-2 border border-slate-300 rounded-lg text-slate-700 bg-white hover:bg-slate-50 transition-colors text-xs">';

import { useState } from 'react';

interface AuthFormProps {
  formType: 'login' | 'signup';
  onToggleForm?: (tab: 'login' | 'signup') => void; // Updated to match parent signature
}

const AuthForm = ({ formType, onToggleForm }: AuthFormProps) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'student',
  });

  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Basic validation
    if (formType === 'signup' && formData.password !== formData.confirmPassword) {
      alert("Passwords don't match!");
      setIsLoading(false);
      return;
    }
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log(`Form (${formType}) submitted:`, formData);
    alert(`${formType === 'login' ? 'Login' : 'Account creation'} successful! (Demo)`);
    setIsLoading(false);
  };  return (
    <form onSubmit={handleSubmit} className="space-y-3 font-sans">
      {/* Social Login Buttons - Compact */}      <div className="grid grid-cols-2 gap-2 mb-4">
        <button
          type="button"
          className="group flex items-center justify-center px-3 py-2 border border-white/60 rounded-lg text-slate-700 bg-white/80 backdrop-blur-sm hover:bg-white/95 transition-all duration-300 text-xs font-semibold shadow-lg shadow-slate-200/50 hover:shadow-xl hover:border-white/80 hover:-translate-y-0.5"
        >
          <svg className="w-3 h-3 mr-1.5 group-hover:scale-110 transition-transform duration-200" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          Google
        </button>        <button
          type="button"
          className="group flex items-center justify-center px-3 py-2 border border-white/60 rounded-lg text-slate-700 bg-white/80 backdrop-blur-sm hover:bg-white/95 transition-all duration-300 text-xs font-semibold shadow-lg shadow-slate-200/50 hover:shadow-xl hover:border-white/80 hover:-translate-y-0.5"
        >
          <svg className="w-3 h-3 mr-1.5 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
          Facebook
        </button>
      </div>      <div className="relative mb-4">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t border-slate-300/60"></div>
        </div>
        <div className="relative flex justify-center text-xs">
          <span className="px-3 bg-gradient-to-r from-white/90 via-white/95 to-white/90 backdrop-blur-sm text-slate-500 font-medium rounded-full">or continue with email</span>
        </div>
      </div>      {formType === 'signup' && (
        <div className="space-y-1">
          <label htmlFor="name" className="block text-xs font-semibold text-slate-700">
            Full Name
          </label>
          <div className="relative">            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-slate-200/80 rounded-lg focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-xs bg-white/90 backdrop-blur-sm hover:border-slate-300 placeholder-slate-400 shadow-sm"
              placeholder="Enter your full name"
              required
            />
            <div className="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
              <svg className="w-3 h-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
          </div>
        </div>
      )}      <div className="space-y-1">
        <label htmlFor="email" className="block text-xs font-semibold text-slate-700">
          Email Address
        </label>
        <div className="relative">          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-slate-200/80 rounded-lg focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-xs bg-white/90 backdrop-blur-sm hover:border-slate-300 placeholder-slate-400 shadow-sm"
            placeholder="Enter your email address"
            required
          />
          <div className="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
            <svg className="w-3 h-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
            </svg>
          </div>
        </div>
      </div>      {formType === 'signup' && (
        <div className="space-y-1">
          <label htmlFor="role" className="block text-xs font-semibold text-slate-700">
            I am a
          </label>
          <div className="relative">            <select
              id="role"
              name="role"
              value={formData.role}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-slate-200/80 rounded-lg focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 bg-white/90 backdrop-blur-sm text-xs hover:border-slate-300 appearance-none cursor-pointer shadow-sm"
            >
              <option value="student">Student</option>
              <option value="teacher">Teacher</option>
              <option value="admin">Administrator</option>
              <option value="parent">Parent</option>
            </select>
            <div className="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
              <svg className="w-3 h-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
              </svg>
            </div>
          </div>
        </div>
      )}      <div className="space-y-1">
        <label htmlFor="password" className="block text-xs font-semibold text-slate-700">
          Password
        </label>
        <div className="relative">          <input
            type={showPassword ? "text" : "password"}
            id="password"
            name="password"
            value={formData.password}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border border-slate-200/80 rounded-lg focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 pr-8 text-xs bg-white/90 backdrop-blur-sm hover:border-slate-300 placeholder-slate-400 shadow-sm"
            placeholder={formType === 'signup' ? 'Create a strong password' : 'Enter your password'}
            required
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute inset-y-0 right-0 pr-2 flex items-center text-slate-400 hover:text-slate-600 transition-colors duration-200"          >
            {showPassword ? (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M9.878 9.878l4.242 4.242M14.12 14.12L15.536 15.536M14.12 14.12L8.464 8.464m5.656 5.656L8.464 8.464" />
              </svg>
            ) : (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            )}
          </button>
        </div>
      </div>      {formType === 'signup' && (
        <div className="space-y-1">
          <label htmlFor="confirmPassword" className="block text-xs font-semibold text-slate-700">
            Confirm Password
          </label>
          <div className="relative">            <input
              type="password"
              id="confirmPassword"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-slate-200/80 rounded-lg focus:ring-2 focus:ring-indigo-500/20 focus:border-indigo-500 transition-all duration-200 text-xs bg-white/90 backdrop-blur-sm hover:border-slate-300 placeholder-slate-400 shadow-sm"
              placeholder="Confirm your password"
              required
            />
            <div className="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
              <svg className="w-3 h-3 text-slate-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
          </div>
        </div>
      )}      {formType === 'login' && (
        <div className="flex items-center justify-between text-xs pt-1">
          <label className="flex items-center text-slate-600 cursor-pointer group">
            <input type="checkbox" className="rounded border-slate-300 text-indigo-600 shadow-sm focus:ring-indigo-500/20 mr-2 w-3 h-3 transition-colors group-hover:border-indigo-400" />
            <span className="group-hover:text-slate-800 transition-colors">Remember me</span>
          </label>
          <a href="#" className="font-semibold text-indigo-600 hover:text-indigo-700 hover:underline transition-all duration-200">
            Forgot password?
          </a>
        </div>
      )}      <div className="pt-1">
        <button
          type="submit"
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-indigo-600 via-purple-600 to-indigo-700 text-white py-2.5 px-5 rounded-lg font-semibold hover:from-indigo-700 hover:via-purple-700 hover:to-indigo-800 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500/50 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-xs shadow-lg shadow-indigo-500/25 hover:shadow-xl hover:shadow-indigo-500/30 hover:-translate-y-0.5 transform"
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              {formType === 'login' ? 'Signing In...' : 'Creating Account...'}
            </>
          ) : (            <>
              {formType === 'login' ? 'Sign In to EduPro' : 'Create My Account'}
              <svg className="w-3 h-3 ml-1.5 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
              </svg>
            </>
          )}
        </button>
      </div>

      {onToggleForm && (
        <div className="text-center text-xs text-slate-600 pt-3 border-t border-slate-100 mt-4">
          {formType === 'login' ? (
            <p>Don't have an account? <button type="button" onClick={() => onToggleForm('signup')} className="font-semibold text-indigo-600 cursor-pointer hover:text-indigo-700 hover:underline transition-all duration-200">Sign up for free</button></p>
          ) : (
            <p>Already have an account? <button type="button" onClick={() => onToggleForm('login')} className="font-semibold text-indigo-600 cursor-pointer hover:text-indigo-700 hover:underline transition-all duration-200">Sign in here</button></p>
          )}
        </div>
      )}
    </form>
  );
};

export default AuthForm;
